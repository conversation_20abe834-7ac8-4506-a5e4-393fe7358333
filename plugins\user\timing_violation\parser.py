"""
时序违例日志解析器

提供vio_summary.log文件解析功能，支持时序违例条目提取和时间单位转换。
"""

import os
import re
from typing import List, Dict, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QThread


class VioLogParser(QObject):
    """时序违例日志解析器"""
    
    def __init__(self):
        super().__init__()
    
    def parse_log_file(self, file_path: str) -> List[Dict]:
        """解析vio_summary.log文件
        
        Args:
            file_path: 日志文件路径
            
        Returns:
            List[Dict]: 解析出的违例列表
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"日志文件不存在: {file_path}")
        
        if not file_path.endswith('vio_summary.log'):
            raise ValueError("请选择vio_summary.log文件")
        
        violations = []
        current_violation = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过空行
                    if not line:
                        continue
                    
                    # 检测分隔线，表示一个违例条目的结束
                    if line.startswith('----'):
                        if current_violation:
                            # 验证违例条目的完整性
                            if self._validate_violation(current_violation):
                                # 转换时间单位并添加到列表
                                processed_violation = self._process_violation(current_violation)
                                violations.append(processed_violation)
                            else:
                                print(f"警告: 第{line_num}行附近的违例条目不完整: {current_violation}")
                            current_violation = {}
                        continue
                    
                    # 解析键值对
                    if ' : ' in line:
                        key, value = line.split(' : ', 1)
                        key = key.strip()
                        value = value.strip()
                        current_violation[key] = value
                    else:
                        # 处理可能的多行内容
                        if current_violation and 'Check' in current_violation:
                            current_violation['Check'] += ' ' + line
                
                # 处理最后一个违例条目
                if current_violation:
                    if self._validate_violation(current_violation):
                        processed_violation = self._process_violation(current_violation)
                        violations.append(processed_violation)
                    else:
                        print(f"警告: 文件末尾的违例条目不完整: {current_violation}")
        
        except Exception as e:
            raise ValueError(f"解析文件失败: {str(e)}")
        
        if not violations:
            raise ValueError("文件中没有找到有效的时序违例记录")
        
        print(f"成功解析 {len(violations)} 条时序违例记录")
        return violations
    
    def _validate_violation(self, violation: Dict) -> bool:
        """验证违例条目的完整性
        
        Args:
            violation: 违例条目字典
            
        Returns:
            bool: 是否有效
        """
        required_fields = ['NUM', 'Hier', 'Time', 'Check']
        return all(field in violation and violation[field] for field in required_fields)
    
    def _process_violation(self, violation: Dict) -> Dict:
        """处理违例条目，转换时间单位等
        
        Args:
            violation: 原始违例条目
            
        Returns:
            Dict: 处理后的违例条目
        """
        processed = violation.copy()
        
        # 转换NUM为整数
        try:
            processed['NUM'] = int(violation['NUM'])
        except ValueError:
            processed['NUM'] = 0
        
        # 转换时间单位
        time_str = violation['Time']
        processed['time_fs'] = self.convert_time_to_fs(time_str)
        processed['time_ns'] = self.convert_time_to_ns(time_str)
        
        return processed
    
    def convert_time_to_fs(self, time_str: str) -> int:
        """转换时间单位到飞秒
        
        Args:
            time_str: 时间字符串，如 "1523423 FS"
            
        Returns:
            int: 飞秒数
        """
        time_str = time_str.upper().strip()
        
        # 提取数字和单位
        match = re.match(r'(\d+(?:\.\d+)?)\s*([A-Z]*)', time_str)
        if not match:
            print(f"警告: 无法解析时间格式: {time_str}")
            return 0
        
        value_str, unit = match.groups()
        try:
            value = float(value_str)
        except ValueError:
            print(f"警告: 无法解析时间数值: {value_str}")
            return 0
        
        # 转换到飞秒
        if unit == 'FS' or unit == '':
            return int(value)
        elif unit == 'PS':
            return int(value * 1000)
        elif unit == 'NS':
            return int(value * 1000000)
        else:
            print(f"警告: 未知的时间单位: {unit}")
            return int(value)  # 假设为飞秒
    
    def convert_time_to_ns(self, time_str: str) -> float:
        """转换时间单位到纳秒
        
        Args:
            time_str: 时间字符串，如 "1523423 FS"
            
        Returns:
            float: 纳秒数
        """
        time_str = time_str.upper().strip()
        
        # 提取数字和单位
        match = re.match(r'(\d+(?:\.\d+)?)\s*([A-Z]*)', time_str)
        if not match:
            print(f"警告: 无法解析时间格式: {time_str}")
            return 0.0
        
        value_str, unit = match.groups()
        try:
            value = float(value_str)
        except ValueError:
            print(f"警告: 无法解析时间数值: {value_str}")
            return 0.0
        
        # 转换到纳秒
        if unit == 'NS' or unit == '':
            return value
        elif unit == 'PS':
            return value / 1000
        elif unit == 'FS':
            return value / 1000000
        else:
            print(f"警告: 未知的时间单位: {unit}")
            return value  # 假设为纳秒
    
    def format_time_display(self, time_fs: int) -> str:
        """格式化时间显示
        
        Args:
            time_fs: 飞秒数
            
        Returns:
            str: 格式化的时间字符串
        """
        if time_fs >= 1000000:
            # 显示为纳秒
            ns = time_fs / 1000000
            return f"{ns:.3f} ns"
        elif time_fs >= 1000:
            # 显示为皮秒
            ps = time_fs / 1000
            return f"{ps:.3f} ps"
        else:
            # 显示为飞秒
            return f"{time_fs} fs"


class AsyncVioLogParser(QThread):
    """异步时序违例日志解析器"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度百分比, 状态信息
    parsing_completed = pyqtSignal(list)     # 解析完成，返回违例列表
    parsing_failed = pyqtSignal(str)         # 解析失败，返回错误信息
    
    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path
        self.parser = VioLogParser()
    
    def run(self):
        """异步解析线程主函数"""
        try:
            self.progress_updated.emit(0, "开始解析文件...")
            
            # 检查文件
            if not os.path.exists(self.file_path):
                self.parsing_failed.emit(f"文件不存在: {self.file_path}")
                return
            
            self.progress_updated.emit(10, "验证文件格式...")
            
            # 解析文件
            violations = self.parser.parse_log_file(self.file_path)
            
            self.progress_updated.emit(90, f"解析完成，共找到 {len(violations)} 条违例记录")
            
            # 发送结果
            self.parsing_completed.emit(violations)
            self.progress_updated.emit(100, "解析完成")
            
        except Exception as e:
            self.parsing_failed.emit(str(e))


class CaseInfoParser:
    """用例信息解析器"""
    
    # 支持的corner列表
    VALID_CORNERS = [
        'npg_f1_ssg', 'npg_f2_ssg', 'npg_f3_ssg', 'npg_f4_ssg', 'npg_f5_ssg', 'npg_f6_ssg', 'npg_f7_ssg',
        'npg_f1_ffg', 'npg_f2_ffg', 'npg_f3_ffg', 'npg_f4_ffg', 'npg_f5_ffg', 'npg_f6_ffg', 'npg_f7_ffg',
        'npg_f1_tt', 'npg_f2_tt', 'npg_f3_tt'
    ]
    
    @staticmethod
    def parse_directory_name(dir_path: str) -> Dict[str, Optional[str]]:
        """从目录路径解析用例信息

        支持以下格式：
        1. {case_name}_{corner} - 如: test_case_npg_f1_ssg
        2. {case_name}_{corner}_xxx - 如: test_case_npg_f1_ffg_cloud
        3. {case_name} - 如: test_case (无corner信息)

        Args:
            dir_path: 目录路径

        Returns:
            Dict: 包含case_name和corner的字典
        """
        dir_name = os.path.basename(dir_path.rstrip(os.sep))
        print(f"解析目录名: {dir_name}")

        # 按优先级尝试匹配corner（从长到短，避免短corner被长corner包含的问题）
        sorted_corners = sorted(CaseInfoParser.VALID_CORNERS, key=len, reverse=True)

        for corner in sorted_corners:
            corner_pattern = f'_{corner}'

            # 方式1: 检查是否以 _{corner} 结尾 (格式: {case_name}_{corner})
            if dir_name.endswith(corner_pattern):
                case_name = dir_name[:-len(corner_pattern)]
                print(f"匹配格式1 - case_name: {case_name}, corner: {corner}")
                return {
                    'case_name': case_name,
                    'corner': corner
                }

            # 方式2: 检查是否包含 _{corner}_ (格式: {case_name}_{corner}_xxx)
            corner_pattern_with_suffix = f'_{corner}_'
            if corner_pattern_with_suffix in dir_name:
                # 找到corner的位置
                corner_pos = dir_name.find(corner_pattern_with_suffix)
                case_name = dir_name[:corner_pos]
                print(f"匹配格式2 - case_name: {case_name}, corner: {corner}")
                return {
                    'case_name': case_name,
                    'corner': corner
                }

        # 如果没有匹配到corner，返回整个目录名作为case_name
        print(f"未匹配到corner - case_name: {dir_name}, corner: None")
        return {
            'case_name': dir_name,
            'corner': None
        }
    
    @staticmethod
    def get_valid_corners() -> List[str]:
        """获取有效的corner列表
        
        Returns:
            List[str]: corner列表
        """
        return CaseInfoParser.VALID_CORNERS.copy()
